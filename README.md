## 临时邮箱管理系统技术架构与需求文档

### 一、系统概述

基于 Cloudflare 全家桶的临时邮箱系统，提供一次性邮箱生成、邮件接收、配额管理等功能，支持多域名后缀选择。

---

### 二、技术架构

#### 前端架构

```mermaid
graph LR
A[Vue3] --> B[Vite]
A --> C[UnoCSS]
A --> D[Element Plus]
B --> E[SSR渲染]
C --> F[原子化样式]
D --> G[UI组件库]
```

#### 后端架构

```mermaid
graph TD
Cloudflare[Cloudflare 全家桶] --> Pages[Pages]
Cloudflare --> Workers[Workers]
Cloudflare --> D1[D1数据库]
Cloudflare --> Email[Email Routing]
Workers --> KV[KV存储]
```

#### 全栈架构图

```mermaid
graph LR
 用户 -->|HTTP请求| CF_Pages[Cloudflare Pages]
 CF_Pages -->|API调用| CF_Workers[Cloudflare Workers]
 CF_Workers --> D1[(D1数据库)]
 CF_Workers --> KV[KV存储]
 SMTP[邮件服务器] -->|转发邮件| Email_Routing[Email Routing]
 Email_Routing -->|触发| CF_Workers
```

---

### 三、数据库设计（D1 SQLite）

#### 表结构

```sql
-- 用户表
CREATE TABLE users (
  id INTEGER PRIMARY KEY,
  username TEXT UNIQUE,
  password_hash TEXT,
  quota INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 临时邮箱表
CREATE TABLE temp_emails (
  id INTEGER PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  email TEXT UNIQUE, -- 完整邮箱地址
  domain_id INTEGER REFERENCES domains(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  active BOOLEAN DEFAULT 1
);

-- 域名表
CREATE TABLE domains (
  id INTEGER PRIMARY KEY,
  domain TEXT UNIQUE, -- example.com
  status INTEGER DEFAULT 1 -- 0=禁用 1=启用
);

-- 邮件表
CREATE TABLE emails (
  id INTEGER PRIMARY KEY,
  temp_email_id INTEGER REFERENCES temp_emails(id),
  sender TEXT,
  subject TEXT,
  content TEXT,
  received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 兑换码表
CREATE TABLE redeem_codes (
  code TEXT PRIMARY KEY,
  quota INTEGER,
  valid_until TIMESTAMP,
  used BOOLEAN DEFAULT 0
);

-- 操作日志表
CREATE TABLE logs (
  id INTEGER PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  action TEXT, -- CREATE_EMAIL/DELETE_EMAIL
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 四、核心功能模块

#### 1. 用户系统

- JWT 身份认证
- 配额管理（创建邮箱-5 配额）
- 个人中心（邮箱列表/邮件查看）

#### 2. 临时邮箱服务

```mermaid
sequenceDiagram
 用户->>Workers: 创建临时邮箱请求
 Workers->>D1: 扣除配额+生成记录
 Workers-->>用户: 返回邮箱地址(<EMAIL>)
 外部邮件->>Email Routing: 发送到**************
 Email Routing->>Workers: 触发邮件处理Worker
 Workers->>D1: 存储邮件内容
 Workers->>用户: WebSocket实时推送
```

#### 3. 管理后台

- 用户管理（禁用/配额调整）
- 域名管理（添加/启用/禁用）
- 邮件审查（查看所有邮件）
- 日志审计（操作记录）
- 兑换码生成（批量创建）

#### 4. 配额系统

- 兑换码兑换流程：

```
1. 用户输入兑换码
2. Workers验证有效性
3. D1更新用户配额
4. 标记兑换码已使用
```

---

### 五、Cloudflare Workers API 设计

#### 认证路由

```javascript
// workers/auth.js
export default {
  async login(request, env) {
    // JWT生成逻辑
    return new Response(JSON.stringify({ token }));
  },
};
```

#### 邮箱路由

```javascript
// workers/email.js
export default {
  async createEmail(request, env) {
    // 检查配额
    // 生成随机邮箱前缀
    // 保存到D1
  },

  async receiveEmail(request, env) {
    // 解析Email Routing转发内容
    // 存储邮件到D1
    // 实时推送给用户
  },
};
```

---

### 六、关键实现细节

#### 邮件接收流程

1. 在 Cloudflare 配置 Email Routing
2. 设置 Catch-all 地址指向 Worker
3. Worker 处理原始邮件：

```javascript
import PostalMime from "postal-mime";
// 邮件处理器
// 使用 postal-mime 解析邮件
async function parseEmailWithPostalMime(rawEmail) {
  try {
    const parser = new PostalMime();
    const email = await parser.parse(rawEmail);
    return email;
  } catch (error) {
    console.error("Email parsing error:", error);
    return {
      subject: "解析失败",
      text: "邮件解析失败",
      html: "",
      from: { address: "", name: "" },
    };
  }
}
function extractVerificationCode(content) {
  // 常见的验证码模式
  const patterns = [
    /验证码[：:\s]*([0-9]{4,8})/i,
    /verification code[：:\s]*([0-9]{4,8})/i,
    /code[：:\s]*([0-9]{4,8})/i,
    /pin[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{4,8})\b.*验证/i,
    /\b([0-9]{4,8})\b.*code/i,
    /您的验证码是[：:\s]*([0-9]{4,8})/i,
    /your verification code is[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{6})\b/g, // 6位数字（最常见的验证码格式）
  ];

  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}


export async function handleEmailProcessing(message, env) {
  try {
    console.log("Processing email from:", message.from, "to:", message.to);

    // 使用 postal-mime 解析邮件
    const parsedEmail = await parseEmailWithPostalMime(message.raw);

    // 提取收件人邮箱地址
    const toEmail = message.to;
    console.log("Recipient email:", toEmail);

    // 提取邮件内容
    const subject = parsedEmail.subject || "无主题";
    const textContent = parsedEmail.text || "";
    const htmlContent = parsedEmail.html || "";

    // 提取发件人信息
    const fromAddress = parsedEmail.from?.address || message.from;
    const fromName = parsedEmail.from?.name || "";

    // 尝试提取验证码
    const verificationCode = extractVerificationCode(
      textContent + " " + htmlContent
    );

    // 首先查找用户临时邮箱记录
    const userTempEmail = await env.DB.prepare(
      'SELECT * FROM user_temp_emails WHERE full_email = ? AND status = "active"'
    )
      .bind(toEmail)
      .first();

    if (userTempEmail) {
      // 处理用户临时邮箱
      await handleUserTempEmailMessage(env, userTempEmail, {
        fromAddress,
        fromName,
        toEmail,
        subject,
        textContent,
        htmlContent,
        verificationCode,
      });
      console.log("User temp email processed successfully for:", toEmail);
      return;
    }

    // 查找 ApiKeyMail 邮箱记录（现有系统）
    const apiKeyEmail = await env.DB.prepare(
      'SELECT * FROM emails WHERE (mail || "@" || domain) = ?'
    )
      .bind(toEmail)
      .first();

    if (apiKeyEmail) {
      // 处理 ApiKeyMail 邮箱（保持现有逻辑）
      await handleApiKeyEmailMessage(env, apiKeyEmail, {
        fromAddress,
        fromName,
        toEmail,
        subject,
        textContent,
        htmlContent,
        verificationCode,
      });
      console.log("ApiKey email processed successfully for:", toEmail);
      return;
    }

    console.log("No email record found for:", toEmail);
  } catch (error) {
    console.error("Email processing error:", error);
  }
}
```

---

### 七、部署配置

#### wrangler.toml

```toml
name = "temp-email-system"
compatibility_date = "2024-03-01"

[[d1_databases]]
binding = "DB"
database_name = "email-db"
database_id = "xxxx-xxxx-xxxx"

[build]
command = "npm run build"

[env.production]
vars = {
  JWT_SECRET = "xxx",
  BASE_DOMAIN = "yourapp.com"
}
```

---

### 八、安全设计

1. **JWT 认证**：所有 API 请求需携带 Authorization 头
2. **配额校验**：创建邮箱时原子操作校验
3. **邮件隔离**：用户只能访问自己的邮件
4. **CORS 限制**：仅允许 Pages 域名访问
5. **操作审计**：所有敏感操作记录日志

---

### 九、扩展性设计

1. 使用 KV 存储缓存高频数据（用户配额/域名列表）
2. 域名负载均衡：根据域名使用率自动分配
3. 邮件清理 Worker（定时删除 7 天前的邮件）
4. 配额回收机制（24 小时未激活邮箱返还配额）

---

### 十、测试方案

1. 单元测试：Vitest 测试核心逻辑
2. Workers 测试：wrangler dev 本地测试
3. 邮件模拟：MailSlurp 测试邮件流
4. 压力测试：artillery 模拟高并发

---

[系统已具备完整实现基础，可立即启动开发]

```mermaid
pie
  title 技术栈分布
  “前端Vue3” ： 35
  “Cloudflare Workers” ： 40
  “D1数据库” ： 15
  “其他服务” ： 10
```
